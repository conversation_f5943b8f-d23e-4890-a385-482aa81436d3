#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据处理程序
完成基本的数据分类和汇总
"""

import pandas as pd
from pathlib import Path
import re
import json
import warnings
warnings.filterwarnings('ignore')

def main():
    print("开始快速数据处理...")
    
    # 加载数据
    df = pd.read_excel('test_data.xlsx')
    print(f'加载数据: {len(df)} 条记录')
    
    # 加载学校名称映射
    try:
        with open('school_mapping.json', 'r', encoding='utf-8') as f:
            school_mapping = json.load(f)
        print(f'加载学校名称映射: {len(school_mapping)} 个映射')
    except:
        school_mapping = {}
        print('未找到学校名称映射文件')
    
    # 应用已有的学校名称标准化
    def standardize_school(school_name):
        if pd.isna(school_name):
            return ''
        school_name = str(school_name).strip()
        return school_mapping.get(school_name, school_name)
    
    df['标准化学校'] = df['学校'].apply(standardize_school)
    
    # 统计学校分布
    school_counts = df['标准化学校'].value_counts()
    print(f'\n共有 {len(school_counts)} 个学校')
    print('\n学校分布（前15）:')
    for school, count in school_counts.head(15).items():
        print(f'{school}: {count} 条记录')
    
    # 创建输出目录
    output_dir = Path('output')
    output_dir.mkdir(exist_ok=True)
    
    # 保存前10个学校的数据
    print('\n开始保存学校数据...')
    for i, (school, count) in enumerate(school_counts.head(10).items()):
        if school and school.strip():
            school_data = df[df['标准化学校'] == school].copy()
            
            # 清理文件名
            filename = f'{school}-导师评价汇总.xlsx'
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
            
            file_path = output_dir / filename
            school_data.to_excel(file_path, index=False)
            print(f'{i+1}. 保存 {school}: {len(school_data)} 条记录')
    
    # 保存总汇总
    df.to_excel(output_dir / '所有导师评价汇总.xlsx', index=False)
    print(f'\n保存总汇总: {len(df)} 条记录')
    
    # 生成简单统计
    print('\n=== 处理完成统计 ===')
    print(f'总记录数: {len(df)}')
    print(f'学校数量: {len(school_counts)}')
    print(f'数据来源: {list(df["数据来源"].unique())}')
    print(f'输出目录: {output_dir.absolute()}')
    
    # 列出生成的文件
    print('\n生成的文件:')
    for file_path in output_dir.glob('*.xlsx'):
        print(f'  {file_path.name}')

if __name__ == "__main__":
    main()
