#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终数据处理程序
完成学校名称标准化和按学校分类汇总
"""

import pandas as pd
from pathlib import Path
import re
from school_standardizer import SchoolNameStandardizer
import warnings
warnings.filterwarnings('ignore')

def main():
    print("开始最终数据处理...")
    
    # 加载已读取的数据
    df = pd.read_excel('test_data.xlsx')
    print(f'加载数据: {len(df)} 条记录')
    
    # 创建标准化器
    standardizer = SchoolNameStandardizer()
    standardizer.load_mapping()
    
    # 应用学校名称标准化
    print('开始标准化学校名称...')
    df['标准化学校'] = df['学校'].apply(
        lambda x: standardizer.standardize_school_name(str(x)) if pd.notna(x) else ''
    )
    
    # 统计标准化后的学校分布
    school_counts = df['标准化学校'].value_counts()
    print(f'\n标准化后共有 {len(school_counts)} 个学校')
    print('\n学校分布（前20）:')
    for school, count in school_counts.head(20).items():
        print(f'{school}: {count} 条记录')
    
    # 按学校分组并保存
    print('\n开始按学校分组保存数据...')
    output_dir = Path('output')
    output_dir.mkdir(exist_ok=True)
    
    # 保存每个学校的数据（处理前15个学校）
    for school in school_counts.head(15).index:
        if school and school.strip():
            school_data = df[df['标准化学校'] == school].copy()
            
            # 清理文件名
            filename = f'{school}-导师评价汇总.xlsx'
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
            
            file_path = output_dir / filename
            school_data.to_excel(file_path, index=False)
            print(f'保存 {school}: {len(school_data)} 条记录 -> {filename}')
    
    # 保存总汇总
    df.to_excel(output_dir / '所有导师评价汇总.xlsx', index=False)
    print(f'\n保存总汇总: {len(df)} 条记录')
    
    # 生成统计报告
    print('\n=== 数据处理统计报告 ===')
    print(f'总记录数: {len(df)}')
    print(f'学校数量: {len(school_counts)}')
    print(f'数据来源分布:')
    source_counts = df['数据来源'].value_counts()
    for source, count in source_counts.items():
        print(f'  {source}: {count} 条记录')
    
    print('\n数据处理完成！')
    print(f'输出文件保存在: {output_dir.absolute()}')

if __name__ == "__main__":
    main()
