#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校名称标准化模块
使用DeepSeek API智能识别和标准化学校名称
"""

import requests
import json
import pandas as pd
from collections import Counter
import time

class SchoolNameStandardizer:
    def __init__(self, api_key='sk-578e180aa00f43a1916c7072825d483f'):
        self.api_key = api_key
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'
        self.school_mapping = {}

    def call_deepseek_api(self, prompt):
        """调用DeepSeek API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.1
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=data)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f'API调用失败: {response.status_code}')
                return None
        except Exception as e:
            print(f'API调用异常: {e}')
            return None

    def standardize_school_name(self, school_name):
        """标准化单个学校名称"""
        if not school_name or school_name.strip() == '':
            return ''

        # 如果已经有映射，直接返回
        if school_name in self.school_mapping:
            return self.school_mapping[school_name]

        prompt = f'''请帮我标准化这个学校名称："{school_name}"

请按照以下规则：
1. 如果是完整的大学名称，直接返回
2. 如果是简称，请返回完整的官方名称
3. 如果是错别字或变体，请返回正确的名称
4. 只返回标准化后的学校名称，不要其他解释

例如：
- 西电 -> 西安电子科技大学
- 北大 -> 北京大学
- 清华 -> 清华大学
- 上交 -> 上海交通大学

学校名称：{school_name}
标准化名称：'''

        result = self.call_deepseek_api(prompt)
        if result:
            standardized_name = result.strip()
            self.school_mapping[school_name] = standardized_name
            return standardized_name
        else:
            # API调用失败，返回原名称
            return school_name

    def batch_standardize(self, school_names, delay=1):
        """批量标准化学校名称"""
        unique_names = list(set(school_names))
        print(f'需要标准化 {len(unique_names)} 个唯一学校名称')

        for i, name in enumerate(unique_names):
            if name and name.strip():
                print(f'处理 {i+1}/{len(unique_names)}: {name}')
                standardized = self.standardize_school_name(name)
                print(f'  -> {standardized}')

                # 添加延迟避免API限制
                if delay > 0:
                    time.sleep(delay)

        return self.school_mapping

    def save_mapping(self, filename='school_mapping.json'):
        """保存学校名称映射"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.school_mapping, f, ensure_ascii=False, indent=2)
        print(f'学校名称映射已保存到: {filename}')

    def load_mapping(self, filename='school_mapping.json'):
        """加载学校名称映射"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.school_mapping = json.load(f)
            print(f'学校名称映射已从 {filename} 加载')
        except FileNotFoundError:
            print(f'映射文件 {filename} 不存在')

if __name__ == "__main__":
    # 测试学校名称标准化
    print('开始测试学校名称标准化...')

    # 读取测试数据
    df = pd.read_excel('test_data.xlsx')
    school_names = df['学校'].dropna().unique()

    print(f'发现 {len(school_names)} 个唯一学校名称')
    print('前10个学校名称:', school_names[:10])

    # 统计学校名称频率
    school_counts = Counter(df['学校'].dropna())
    print('\n学校名称频率统计（前20）:')
    for school, count in school_counts.most_common(20):
        print(f'{school}: {count}')

    # 创建标准化器
    standardizer = SchoolNameStandardizer()

    # 先测试几个常见的简称
    test_names = ['西电', '北大', '清华', '上交', '华科', '中科大']
    print('\n测试常见简称标准化:')
    for name in test_names:
        if name in school_names:
            standardized = standardizer.standardize_school_name(name)
            print(f'{name} -> {standardized}')

    print('\n学校名称标准化测试完成')